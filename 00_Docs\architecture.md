Analyze the existing codebase and implement a comprehensive project-based chatbot management system with the following specific requirements:

## Phase 1: Codebase Analysis & Architecture Planning
1. **Examine the current application structure** to understand:
   - Existing authentication system (Firebase Auth)
   - Current chat interface implementation
   - Data storage patterns and Firebase/Firestore usage
   - Component architecture and routing structure
   - API integration patterns (Gemini API usage)

2. **Create a detailed implementation plan** that preserves existing functionality while adding the new project-based workflow

## Phase 2: Core Infrastructure Implementation

### 2.1 Database Schema Design
Implement the following Firestore collections structure:
```
/users/{userId}
  - email, createdAt, displayName

/projects/{projectId}
  - ownerId: userId reference
  - name: string
  - websiteUrl: string (optional)
  - status: "training" | "ready" | "inactive"
  - config: {
      themeColor: string (hex color)
      welcomeMessage: string
      suggestedQuestions: string[]
      systemPrompt: string
      headerLogo?: string (URL)
    }
  - knowledgeBaseStats: { chunkCount: number }
  - createdAt, updatedAt: timestamps

/projects/{projectId}/chunks/{chunkId}
  - text: string
  - embedding: number[]
  - sourceType: "url" | "text" | "file"
  - sourceReference: string
  - createdAt: timestamp
```

### 2.2 Authentication Flow Modification
- Redirect authenticated users to Project Dashboard instead of direct chat interface
- Maintain existing Firebase Auth implementation
- Add route protection for project-specific pages

## Phase 3: User Interface Implementation

### 3.1 Project Dashboard (New Landing Page)
Create a responsive dashboard with:
- **Header**: User profile, logout, app branding
- **Main Content**: 
  - Grid layout of project cards (responsive: 1-3 columns based on screen size)
  - Prominent "Create New Project" button
  - Empty state message for new users
- **Project Cards**: Display project name, status badge, knowledge base stats, last updated
- **Navigation**: Click handlers to enter project management view

### 3.2 Project Creation Modal
Implement a modal form with:
- Project name input (required, max 50 characters)
- Website URL input (optional, with URL validation)
- Form validation and error handling
- Success feedback and automatic redirect to project management

### 3.3 Project Management Interface
Create a tabbed interface with:

**Tab 1: Knowledge Base Management**
- **Data Sources Section**:
  - URL input with "Add URL" button (supports multiple URLs)
  - Text paste area (existing functionality)
  - File upload component (.txt, .md, .pdf support)
  - List of added sources with remove option
- **Training Controls**:
  - "Build Knowledge Base" button
  - Progress indicator with specific status messages
  - Training status display
- **Knowledge Base Overview**:
  - Chunk count and statistics
  - Last training date
  - "Rebuild" option for updates

**Tab 2: Chatbot Configuration**
- **Appearance Settings**:
  - Color picker for theme color
  - Logo upload component (with image preview)
  - Welcome message text area
- **Behavior Settings**:
  - System prompt editor (pre-filled with current prompt)
  - Suggested questions manager (add/remove up to 4 questions)
- **Preview Component**: Live preview of chat widget appearance

**Tab 3: Integration & Deployment**
- **Embed Code Generator**:
  - Display HTML/JavaScript snippet with project-specific ID
  - Copy-to-clipboard functionality
  - Integration instructions
- **Testing Tools**:
  - Preview link for testing the chatbot
  - QR code for mobile testing

## Phase 4: Backend Services Implementation

### 4.1 Knowledge Base Processing Service
- **URL Scraping**: Implement web scraping for provided URLs
- **File Processing**: Handle uploaded files (.txt, .md, .pdf)
- **Text Chunking**: Split content into optimal chunks for embeddings
- **Embedding Generation**: Use existing Gemini API integration
- **Vector Storage**: Save embeddings to Firestore with metadata

### 4.2 Chat API Enhancement
- Modify existing chat endpoint to:
  - Accept projectId parameter
  - Fetch project-specific knowledge base
  - Apply project-specific configuration
  - Use project-specific system prompt

### 4.3 Embed Script Development
Create `embed.js` script that:
- Reads `data-project-id` attribute
- Injects floating chat icon with project branding
- Opens iframe with project-specific chat interface
- Handles responsive design and mobile compatibility

## Phase 5: Public Chat Interface
- Create a public-facing chat interface accessible via iframe
- Fetch project configuration and branding
- Implement the chat functionality using project-specific knowledge base
- Apply custom styling based on project configuration

## Implementation Requirements:
1. **Preserve existing functionality**: Ensure current chat features continue working
2. **Mobile responsiveness**: All new interfaces must work on mobile devices
3. **Error handling**: Comprehensive error handling for all user interactions
4. **Loading states**: Proper loading indicators for all async operations
5. **Data validation**: Client and server-side validation for all inputs
6. **Security**: Proper authentication and authorization for project access
7. **Performance**: Efficient data fetching and caching strategies

## Testing Strategy:
- Unit tests for new components and services
- Integration tests for the complete workflow
- End-to-end tests for the embed script functionality
- Manual testing of the complete user journey

Implement this system incrementally, starting with the database schema and authentication flow, then building the UI components, and finally implementing the backend services and embed functionality.