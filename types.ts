
export enum MessageRole {
  USER = 'user',
  MODEL = 'model',
  SYSTEM = 'system',
}

export interface ChatMessage {
  role: MessageRole;
  content: string;
  timestamp: number;
}

export interface VectorData {
  text: string;
  embedding: number[];
}

// Project-related types
export enum ProjectStatus {
  TRAINING = 'training',
  READY = 'ready',
  INACTIVE = 'inactive',
}

export enum SourceType {
  URL = 'url',
  TEXT = 'text',
  FILE = 'file',
}

export interface User {
  id: string;
  email: string;
  displayName?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProjectConfig {
  themeColor: string;
  welcomeMessage: string;
  suggestedQuestions: string[];
  systemPrompt: string;
  headerLogo?: string;
}

export interface KnowledgeBaseStats {
  chunkCount: number;
  lastTrainingDate?: Date;
}

export interface Project {
  id: string;
  ownerId: string;
  name: string;
  websiteUrl?: string;
  status: ProjectStatus;
  config: ProjectConfig;
  knowledgeBaseStats: KnowledgeBaseStats;
  createdAt: Date;
  updatedAt: Date;
}

export interface KnowledgeChunk {
  id: string;
  projectId: string;
  text: string;
  embedding: number[];
  sourceType: SourceType;
  sourceReference: string;
  createdAt: Date;
}

export interface DataSource {
  id: string;
  type: SourceType;
  reference: string;
  content?: string; // Store the actual content for TEXT and FILE types
  addedAt: Date;
}
