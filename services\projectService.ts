import { 
  Project, 
  ProjectConfig, 
  ProjectStatus, 
  KnowledgeChunk, 
  SourceType,
  VectorData,
  DataSource 
} from '../types';
import {
  createProject,
  getProject,
  getUserProjects,
  updateProject,
  deleteProject,
  createKnowledgeChunksBatch,
  getProjectChunks,
  deleteProjectChunks,
  getChunkCount,
} from './firestoreService';
import { embedChunks } from './geminiService';
import { chunkText } from '../utils/textChunking';

// Default project configuration
const DEFAULT_PROJECT_CONFIG: ProjectConfig = {
  themeColor: '#4f46e5',
  welcomeMessage: "Hello! I'm here to help you with any questions you might have. How can I assist you today?",
  suggestedQuestions: [
    "What are your business hours?",
    "How can I contact support?",
    "What products do you offer?",
    "What is your return policy?"
  ],
  systemPrompt: `You are a helpful customer support assistant. 
Your goal is to provide helpful and accurate answers based *only* on the context provided.
If the context does not contain the answer to the question, state that you don't have enough information to answer.
Do not make up information or answer based on prior knowledge.
Provide a comprehensive and detailed answer. Explain your reasoning and quote relevant parts from the context when possible.`,
};

// Project creation and management
export const createNewProject = async (
  ownerId: string,
  name: string,
  websiteUrl?: string
): Promise<string> => {
  try {
    const now = new Date();
    const projectData: Omit<Project, 'id'> = {
      ownerId,
      name: name.trim(),
      status: ProjectStatus.INACTIVE,
      config: { ...DEFAULT_PROJECT_CONFIG },
      knowledgeBaseStats: {
        chunkCount: 0,
      },
      createdAt: now,
      updatedAt: now,
    };

    // Only add websiteUrl if it exists and is not empty
    const trimmedUrl = websiteUrl?.trim();
    if (trimmedUrl) {
      projectData.websiteUrl = trimmedUrl;
    }

    const projectId = await createProject(projectData);
    return projectId;
  } catch (error) {
    console.error('Error creating new project:', error);
    throw new Error('Failed to create project');
  }
};

export const getProjectById = async (projectId: string): Promise<Project | null> => {
  try {
    return await getProject(projectId);
  } catch (error) {
    console.error('Error getting project:', error);
    throw new Error('Failed to get project');
  }
};

export const getProjectsForUser = async (userId: string): Promise<Project[]> => {
  try {
    return await getUserProjects(userId);
  } catch (error) {
    console.error('Error getting user projects:', error);
    throw new Error('Failed to get user projects');
  }
};

export const updateProjectConfig = async (
  projectId: string,
  config: Partial<ProjectConfig>
): Promise<void> => {
  try {
    const project = await getProject(projectId);
    if (!project) {
      throw new Error('Project not found');
    }

    const updatedConfig = { ...project.config, ...config };
    await updateProject(projectId, { config: updatedConfig });
  } catch (error) {
    console.error('Error updating project config:', error);
    throw new Error('Failed to update project configuration');
  }
};

export const updateProjectStatus = async (
  projectId: string,
  status: ProjectStatus
): Promise<void> => {
  try {
    await updateProject(projectId, { status });
  } catch (error) {
    console.error('Error updating project status:', error);
    throw new Error('Failed to update project status');
  }
};

export const deleteProjectById = async (projectId: string): Promise<void> => {
  try {
    await deleteProject(projectId);
  } catch (error) {
    console.error('Error deleting project:', error);
    throw new Error('Failed to delete project');
  }
};

// Knowledge base management
export const processAndStoreContent = async (
  projectId: string,
  content: string,
  sourceType: SourceType,
  sourceReference: string
): Promise<void> => {
  try {
    // Update project status to training
    await updateProjectStatus(projectId, ProjectStatus.TRAINING);

    // Clear existing chunks
    await deleteProjectChunks(projectId);

    // Chunk the content
    const chunks = chunkText(content, 1000, 200);
    
    if (chunks.length === 0) {
      throw new Error('No content chunks were generated');
    }

    // Generate embeddings
    const vectorData: VectorData[] = await embedChunks(chunks);

    // Create knowledge chunks
    const now = new Date();
    const knowledgeChunks: Omit<KnowledgeChunk, 'id'>[] = vectorData.map((vector, index) => ({
      projectId,
      text: vector.text,
      embedding: vector.embedding,
      sourceType,
      sourceReference,
      createdAt: now,
    }));

    // Store chunks in batches
    await createKnowledgeChunksBatch(knowledgeChunks);

    // Update project stats and status
    await updateProject(projectId, {
      status: ProjectStatus.READY,
      knowledgeBaseStats: {
        chunkCount: knowledgeChunks.length,
        lastTrainingDate: now,
      },
    });
  } catch (error) {
    console.error('Error processing and storing content:', error);
    // Reset status to inactive on error
    await updateProjectStatus(projectId, ProjectStatus.INACTIVE);
    throw new Error('Failed to process and store content');
  }
};

export const getProjectKnowledgeBase = async (projectId: string): Promise<VectorData[]> => {
  try {
    const chunks = await getProjectChunks(projectId);
    return chunks.map(chunk => ({
      text: chunk.text,
      embedding: chunk.embedding,
    }));
  } catch (error) {
    console.error('Error getting project knowledge base:', error);
    throw new Error('Failed to get project knowledge base');
  }
};

export const clearProjectKnowledgeBase = async (projectId: string): Promise<void> => {
  try {
    await deleteProjectChunks(projectId);
    await updateProject(projectId, {
      status: ProjectStatus.INACTIVE,
      knowledgeBaseStats: {
        chunkCount: 0,
      },
    });
  } catch (error) {
    console.error('Error clearing project knowledge base:', error);
    throw new Error('Failed to clear project knowledge base');
  }
};

export const getProjectKnowledgeBaseStats = async (projectId: string) => {
  try {
    const chunkCount = await getChunkCount(projectId);
    const project = await getProject(projectId);
    
    return {
      chunkCount,
      lastTrainingDate: project?.knowledgeBaseStats.lastTrainingDate,
      status: project?.status || ProjectStatus.INACTIVE,
    };
  } catch (error) {
    console.error('Error getting knowledge base stats:', error);
    return {
      chunkCount: 0,
      lastTrainingDate: undefined,
      status: ProjectStatus.INACTIVE,
    };
  }
};

// File processing functions
export const processFile = async (file: File): Promise<string> => {
  try {
    const fileType = file.type;
    const fileName = file.name.toLowerCase();

    if (fileType === 'text/plain' || fileName.endsWith('.txt')) {
      return await file.text();
    } else if (fileType === 'text/markdown' || fileName.endsWith('.md')) {
      const content = await file.text();
      // Basic markdown to text conversion (remove markdown syntax)
      return content
        .replace(/#{1,6}\s+/g, '') // Remove headers
        .replace(/\*\*(.*?)\*\*/g, '$1') // Remove bold
        .replace(/\*(.*?)\*/g, '$1') // Remove italic
        .replace(/\[(.*?)\]\(.*?\)/g, '$1') // Remove links, keep text
        .replace(/```[\s\S]*?```/g, '') // Remove code blocks
        .replace(/`(.*?)`/g, '$1') // Remove inline code
        .replace(/^\s*[-*+]\s+/gm, '') // Remove list markers
        .replace(/^\s*\d+\.\s+/gm, '') // Remove numbered list markers
        .trim();
    } else if (fileType === 'application/pdf' || fileName.endsWith('.pdf')) {
      throw new Error('PDF processing is not yet supported. Please convert to text format.');
    } else {
      throw new Error(`Unsupported file type: ${fileType}. Please use .txt or .md files.`);
    }
  } catch (error) {
    console.error('Error processing file:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to process file');
  }
};

// URL content fetching (enhanced)
export const fetchUrlContent = async (url: string): Promise<string> => {
  try {
    // Validate URL format
    new URL(url); // This will throw if URL is invalid

    const proxyUrl = `https://api.allorigins.win/raw?url=${encodeURIComponent(url)}`;
    const response = await fetch(proxyUrl);

    if (!response.ok) {
      throw new Error(`Failed to fetch content. Status: ${response.status}. The website may be blocking bots or the URL is invalid.`);
    }

    const html = await response.text();
    const textContent = parseHtmlToText(html);

    if (!textContent.trim()) {
      throw new Error('No readable content found on the webpage');
    }

    return textContent;
  } catch (error) {
    console.error('Error fetching URL content:', error);
    if (error instanceof TypeError && error.message.includes('Invalid URL')) {
      throw new Error('Please enter a valid URL');
    }
    throw new Error(error instanceof Error ? error.message : 'Failed to fetch content from URL');
  }
};

// HTML parsing (reusing existing logic)
const parseHtmlToText = (html: string): string => {
  try {
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    
    // Remove tags that typically don't contain main content
    doc.querySelectorAll('script, style, nav, header, footer, aside, form, button, [aria-hidden="true"]').forEach(el => el.remove());
    
    const textContent = doc.body.textContent || '';
    return textContent.replace(/\s+/g, ' ').trim();
  } catch (error) {
    console.error('Error parsing HTML:', error);
    throw new Error('Failed to parse HTML content');
  }
};

// Validation helpers
export const validateProjectName = (name: string): string | null => {
  const trimmed = name.trim();
  if (!trimmed) {
    return 'Project name is required';
  }
  if (trimmed.length > 50) {
    return 'Project name must be 50 characters or less';
  }
  return null;
};

export const validateWebsiteUrl = (url: string): string | null => {
  if (!url.trim()) {
    return null; // URL is optional
  }

  try {
    const parsedUrl = new URL(url);
    // Only allow HTTP and HTTPS protocols for website URLs
    if (parsedUrl.protocol !== 'http:' && parsedUrl.protocol !== 'https:') {
      return 'Please enter a valid URL';
    }
    return null;
  } catch {
    return 'Please enter a valid URL';
  }
};


