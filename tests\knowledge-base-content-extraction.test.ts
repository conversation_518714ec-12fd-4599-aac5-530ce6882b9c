/**
 * Tests for the knowledge base content extraction fix
 * Verifies that content is properly stored and extracted from data sources
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { DataSource, SourceType } from '../types';

// Mock the processFile function
const mockProcessFile = vi.fn();
vi.mock('../services/projectService', () => ({
  processFile: mockProcessFile,
}));

describe('Knowledge Base Content Extraction', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('DataSource Content Storage', () => {
    it('should store full text content in DataSource', () => {
      const textContent = 'This is a long piece of text content that should be stored in full, not just the first 100 characters as a preview.';
      
      const dataSource: DataSource = {
        id: '1',
        type: SourceType.TEXT,
        reference: textContent.substring(0, 100) + '...',
        content: textContent,
        addedAt: new Date(),
      };

      expect(dataSource.content).toBe(textContent);
      expect(dataSource.content?.length).toBeGreaterThan(100);
      expect(dataSource.reference.length).toBeLessThanOrEqual(103); // 100 chars + '...'
    });

    it('should store file content in DataSource', async () => {
      const fileContent = 'This is the content of a processed file.';
      mockProcessFile.mockResolvedValue(fileContent);

      // Simulate file processing
      const file = new File(['file content'], 'test.txt', { type: 'text/plain' });
      const processedContent = await mockProcessFile(file);

      const dataSource: DataSource = {
        id: '2',
        type: SourceType.FILE,
        reference: file.name,
        content: processedContent,
        addedAt: new Date(),
      };

      expect(dataSource.content).toBe(fileContent);
      expect(mockProcessFile).toHaveBeenCalledWith(file);
    });

    it('should handle URL sources without content field', () => {
      const dataSource: DataSource = {
        id: '3',
        type: SourceType.URL,
        reference: 'https://example.com',
        addedAt: new Date(),
      };

      // URL sources don't store content locally, they fetch it during processing
      expect(dataSource.content).toBeUndefined();
    });
  });

  describe('Content Extraction Logic', () => {
    it('should extract content from multiple sources', () => {
      const sources: DataSource[] = [
        {
          id: '1',
          type: SourceType.TEXT,
          reference: 'Text source...',
          content: 'This is text content.',
          addedAt: new Date(),
        },
        {
          id: '2',
          type: SourceType.FILE,
          reference: 'document.txt',
          content: 'This is file content.',
          addedAt: new Date(),
        },
      ];

      let allContent = '';
      for (const source of sources) {
        if (source.content && source.content.trim()) {
          allContent += source.content + '\n\n';
        }
      }

      expect(allContent.trim()).toBe('This is text content.\n\nThis is file content.');
      expect(allContent.length).toBeGreaterThan(0);
    });

    it('should handle empty content gracefully', () => {
      const sources: DataSource[] = [
        {
          id: '1',
          type: SourceType.TEXT,
          reference: 'Empty text...',
          content: '',
          addedAt: new Date(),
        },
        {
          id: '2',
          type: SourceType.FILE,
          reference: 'empty.txt',
          content: '   ', // whitespace only
          addedAt: new Date(),
        },
      ];

      let allContent = '';
      const failedSources: string[] = [];

      for (const source of sources) {
        if (source.content && source.content.trim()) {
          allContent += source.content + '\n\n';
        } else {
          failedSources.push(`${source.type.toUpperCase()}: ${source.reference} (no content stored)`);
        }
      }

      expect(allContent.trim()).toBe('');
      expect(failedSources).toHaveLength(2);
      expect(failedSources[0]).toContain('TEXT: Empty text...');
      expect(failedSources[1]).toContain('FILE: empty.txt');
    });

    it('should provide detailed error messages for failed sources', () => {
      const sources: DataSource[] = [
        {
          id: '1',
          type: SourceType.TEXT,
          reference: 'Valid text...',
          content: 'This is valid content.',
          addedAt: new Date(),
        },
        {
          id: '2',
          type: SourceType.FILE,
          reference: 'missing.txt',
          content: undefined, // Missing content
          addedAt: new Date(),
        },
      ];

      let allContent = '';
      const failedSources: string[] = [];

      for (const source of sources) {
        if (source.content && source.content.trim()) {
          allContent += source.content + '\n\n';
        } else {
          failedSources.push(`${source.type.toUpperCase()}: ${source.reference} (no content stored)`);
        }
      }

      expect(allContent.trim()).toBe('This is valid content.');
      expect(failedSources).toHaveLength(1);
      expect(failedSources[0]).toBe('FILE: missing.txt (no content stored)');
    });
  });
});
